package com.score.callmetest.network

import com.score.callmetest.entity.InstallReferrerEntity
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 请求接口，映射表在文件：[callmeso.com映射关系.txt] (../../callmeso.com映射关系.txt)
 *
 */
interface ApiService {

    @POST("/security/oauth")
    suspend fun login(@Body request: LoginRequest): BaseResponse<LoginData>

    /**
     * 获取配置
     */
    @POST("/config/getAppConfigPostV2")
    suspend fun getAppConfigPostV2(@Body body: ConfigPostV2): BaseResponse<AppConfigData>

    /**
     * 校验Token
     */
    @POST("/security/isValidToken")
    suspend fun isValidToken(@Body body: TokenBody): BaseResponse<Boolean>

    /**
     * 获取UserInfo
     */
    @POST("/user/getUserInfoPostV2")
    suspend fun getUserInfoPostV2(@Body body: GetUserInfoPostV2Request): BaseResponse<UserInfo>

    /**
     * 创建通话频道接口
     */
    @POST("/video-call/channel/create")
    suspend fun createChannel(@Body request: CreateChannelRequest): BaseResponse<CreateChannelResponse>

    /**
     * 挂断通话接口
     */
    @POST("/video-call/hangUp")
    suspend fun hangUp(@Body request: HangUpRequest): BaseResponse<Boolean>

    /**
     * 接听通话接口
     */
    @POST("/video-call/pickUp")
    suspend fun pickUp(@Body request: PickUpRequest): BaseResponse<Boolean>

    /**
     *查询用户最近通话记录接口
     */
    @POST("/video-call/channel/latelyRecord")
    suspend fun latelyRecord(@Body request: LatelyRecordRequest): BaseResponse<LatelyRecordResponseObj>

    /**
     * 创建充值订单接口
     */
    @POST("/coin/recharge/create")
    suspend fun createRechargeOrder(@Body request: CreateRechargeRequest): BaseResponse<CreateRechargeResponse>

    /**
     * GooglePlay订单支付校验接口
     */
    @POST("/coin/recharge/payment/gp")
    suspend fun verifyGooglePlayPayment(@Body request: GooglePlayPaymentVerifyRequest): BaseResponse<Boolean>

    /**
     * 查询商品列表接口
     */
    @POST("/coin/goods/listPostV2")
    suspend fun queryGoodsListPostV2(@Body request: QueryGoodsListRequest): BaseResponse<List<GoodsInfo>>

    /**
     * 搜索主播邀请链接的商品列表接口
     */
    @POST("/coin/goods/broadcasterInvitation")
    suspend fun getBroadcasterInvitationGoods(@Body request: BroadcasterInvitationGoodsRequest): BaseResponse<List<GoodsInfo>>

    /**
     * 查询充值结果接口
     */
    @POST("/coin/recharge/search")
    suspend fun searchRechargeResult(@Body request: RechargeSearchRequest): BaseResponse<RechargeSearchResponse>

    /**
     * 下发的参数配置接口
     */
    @GET("/user/getRandomBroadcaster")
    suspend fun getStrategy(): BaseResponse<StrategyConfig>


    /**
     * 下发的参数配置接口
     */
    @POST("/config/getStrategyPostV2")
    suspend fun getStrategyPostV2(): BaseResponse<StrategyConfig>

    /**
     * 搜索主播墙列表接口
     */
    @POST("/broadcaster/wall/search")
    suspend fun searchBroadcasters(@Body request: SearchBroadcastersRequest): BaseResponse<List<BroadcasterModel>>

    /**
     * 获取用户在线状态接口
     */
    @POST("/user/getUserOnlineStatusPostV2")
    suspend fun getUserOnlineStatusPostV2(@Body body: GetUserOnlineStatusPostV2Request): BaseResponse<String>

    /**
     * 获取一批用户在线状态接口
     * @param userIds 用户ID列表
     * @return Map<String, String> key为用户id，value为状态
     */
    @POST("/user/getUserListOnlineStatusPostV2")
    suspend fun getUserListOnlineStatusPostV2(@Body body: GetUserListOnlineStatusPostV2Request): BaseResponse<Map<String, String>>

    /**
     * 退出登录接口
     */
    @POST("/security/logout")
    suspend fun logout(): BaseResponse<Boolean>

    /**
     * 获取用户金币接口
     */
    @POST("/user/getUserCoinsPostV2")
    suspend fun getUserCoins(): BaseResponse<Int>

    /**
     * 更新Agora uid接口
     */
    @POST("/user/updateAgoraUid")
    suspend fun updateAgoraUid(@Body request: UpdateAgoraUidRequest): BaseResponse<Boolean>

    /**
     * 加入频道接口
     */
    @POST("/video-call/channel/join")
    suspend fun joinChannel(@Body request: JoinChannelRequest): BaseResponse<Boolean>

    /**
     * 检查充值链接接口
     */
    @POST("/coin/recharge/checkBroadcasterInvitationPostV2")
    suspend fun checkBroadcasterInvitation(@Query("invitationId") invitationId: String): BaseResponse<Boolean>


    /**
     * 获取主播额外信息接口
     */
    @POST("/user/getBroadcasterExtraInfoPostV2")
    suspend fun getBroadcasterExtraInfoPostV2(@Body body: GetBroadcasterExtraInfoRequest): BaseResponse<BroadcasterExtraInfo>

    /**
     * 主播礼物列表采用分类显示新接口
     * //查询的礼物类型,1:查询普通礼物、4:查询特效礼物、8:查询活动礼物、5:查询普通+特效礼物、9:查询普通+活动礼物、12:查询特效+活动礼物、15:查询全部，(2、3、6，7、11、13、14预留，现1与3等效，现4与6等效，现5与7等效，现8与10等效，现9与11等效,现12与14等效,现13与15等效)
     */
    @POST("/gift/getGiftCount")
    suspend fun getGiftCount(@Body body: GetGiftCountRequest): BaseResponse<GetGiftCountResponse>


    /**
     * 赠送礼物接口
     */
    @POST("/user/giveUserGifts")
    suspend fun giveUserGifts(@Body request: GiveGiftRequest): BaseResponse<GiveGiftResponse>

    /**
     * 获取礼物列表接口
     */
    @POST("/gift/v2/listPostV2")
    suspend fun getGiftListPostV2(@Body request: GetGiftListRequest): BaseResponse<List<GiftInfo>>

    /**
     * 查询通话结果接口
     */
    @POST("/video-call/user/callResult")
    suspend fun getCallResult(@Body request: CallResultRequest): BaseResponse<CallResult>

    /**
     * 查询用户头像接口
     */
    @POST("/user/avatar/search")
    suspend fun searchAvatar(@Body request: AvatarSearchRequest): BaseResponse<AvatarSearchResponse>

    /**
     * 评价主播接口
     */
    @POST("/broadcaster/evaluate/submit")
    suspend fun evaluateBroadcaster(@Body request: BroadcasterEvaluateRequest): BaseResponse<Boolean>

    /**
     * 获取用户的头像和昵称接口
     */
    @GET("/user/getUsernameAndAvatar")
    suspend fun getUsernameAndAvatar(@Query("userId") userId: String): BaseResponse<UserProfileResponse>

    /**
     * 获取通话时长接口
     */
    @POST("/video-call/duration")
    suspend fun getCallDuration(@Body request: CallDurationRequest): BaseResponse<Int>

    /**
     * 获取融云token接口
     */
    @POST("/user/rongcloud/tokenPostV2")
    suspend fun getRongCloudToken(@Body request: RcTokenRequest): BaseResponse<String>

    /**
     * 新人促销商品
     */
    @POST("/coin/goods/getPromotion")
    suspend fun getPromotionGoods(@Body request: PromotionGoodsRequest): BaseResponse<GoodsInfo>

    /**
     * 活动商品信息接口
     */
    @POST("/coin/goods/getLastSpecialOfferV2")
    suspend fun getLastSpecialOfferV2(@Body request: SpecialOfferRequest): BaseResponse<List<LastSpecialOfferResponse>>


    /**
     * 添加好友接口
     */
    @POST("/user/addFriend")
    suspend fun addFriend(@Body request: AddFriendRequest): BaseResponse<Boolean>

    /**
     * 删除好友接口
     */
    @POST("/user/unfriendSoul")
    suspend fun unfriend(@Body request: UnFriendRequest): BaseResponse<Boolean>

//    /**
//     * 获取oss上传权限接口
//     */
//    @GET("/user/oss/policy")
//    suspend fun getOssPolicy(): BaseResponse<OssPolicyResponse>


    /**
     * 获取oss上传权限接口
     */
    @POST("/user/oss/policyPostV2")
    suspend fun getOssPolicyPostV2(): BaseResponse<OssPolicyResponse>

    /**
     * 更新头像接口
     */
    @POST("/user/updateAvatar")
    suspend fun updateAvatar(@Body request: UpdateAvatarRequest): BaseResponse<UpdateAvatarResponse>

    /**
     * 更新媒体资源接口
     */
    @POST("/user/updateMedia")
    suspend fun updateMedia(@Body request: UpdateMediaRequest): BaseResponse<List<UpdateMediaResponse>>

    /**
     * 获取用户语言选择列表接口
     */
    @GET("/user/languageSelect")
    suspend fun getLanguageSelections(): BaseResponse<List<String>>

    /**
     * 保存用户所有信息接口
     */
    @POST("/user/saveUserInfo")
    suspend fun saveUserInfo(@Body request: SaveUserInfoRequest): BaseResponse<Boolean>

    /**
     * 保存用户基本信息接口
     */
    @POST("/user/saveBasicInfo")
    suspend fun saveBasicInfo(@Body request: SaveBasicInfoRequest): BaseResponse<Boolean>

    /**
     * 主播排行榜列表查询接口
     */
    @POST("/broadcaster/rank/search")
    suspend fun searchBroadcasterRank(@Body request: BroadcasterRankSearchRequest): BaseResponse<BroadcasterRankResponse>

    /**
     * 用户排行榜列表查询接口
     */
    @POST("/user/rank/search")
    suspend fun searchUserRank(@Body request: UserRankSearchRequest): BaseResponse<UserRankResponse>

    /**
     * 用户心跳上报接口
     */
    @POST("/user/activeing")
    suspend fun reportUserActive(): BaseResponse<Boolean>

    /**
     * 用户前后台切换上报接口
     */
    @POST("/config/getGuideInConfig")
    suspend fun switchUserMode(@Body request: ModeSwitchRequest): BaseResponse<Boolean>

    /**
     * 投诉与屏蔽接口
     */
    @POST("/report/complain/insertRecord")
    suspend fun insertComplainRecord(@Body request: ComplainInsertRecordRequest): BaseResponse<Boolean>

    /**
     * 取消屏蔽接口
     */
    @POST("/report/complain/removeBlock")
    suspend fun removeBlock(@Body request: RemoveBlockRequest): BaseResponse<Boolean>

    /**
     * 获取屏蔽列表接口
     */
    @POST("/report/complain/blockList")
    suspend fun getBlockList(@Body request: UserBlockListRequest): BaseResponse<List<BlockListItem>>

    /**
     * AF归因主动上报接口
     */
    @POST("/hit/ascribeRecordReqs")
    suspend fun reportAfAttribution(@Body request: AfAttributionRecordRequest): BaseResponse<Boolean>

    /**
     * IM聊天限制上报接口
     */
    @POST("/user/createImSession")
    suspend fun createImSession(@Body request: CreateImSessionRequest): BaseResponse<CreateImSessionResponse>

    /**
     * 用户绑定邮箱接口
     */
    @POST("/user/bindingEmail")
    suspend fun bindEmail(@Body request: BindEmailRequest): BaseResponse<Boolean>

    /**
     * 用户密码确认与修改接口
     */
    @POST("/common/user/password")
    suspend fun manageUserPassword(@Body request: UserPasswordRequest): BaseResponse<Boolean>

    /**
     * 一键关注打招呼接口
     */
    @POST("/user/RecommendBroadcaster")
    suspend fun getRecommendedBroadcasters(): BaseResponse<List<RecommendedBroadcasterResponse>>

    /**
     * 一键关注打招呼接口
     */
    @POST("/user/recommendBroadcaster/send")
    suspend fun sendRecommendedBroadcaster(@Body request: SendRecommendedBroadcasterRequest): BaseResponse<Boolean>

    /**
     * 通知后台机器人呼叫接口
     */
    @POST("/security/existsByEmailPostV2")
    suspend fun notifyRobotCall(@Body request: RobotActionRequest): BaseResponse<Boolean>

    /**
     * 通知后台机器人im接口
     */
    @POST("/user/robotIM")
    suspend fun notifyRobotIM(@Body request: RobotActionRequest): BaseResponse<Boolean>

    /**
     * 用户跳转界面上报接口
     */
    @POST("/user/skipPage")
    suspend fun reportSkipPage(@Body request: SkipPageRequest): BaseResponse<Boolean>

    /**
     * 批量查询图片远程url
     */
    @POST("/user/getMediaUrl")
    suspend fun getMediaUrls(@Body request: List<MediaUrlRequest>): BaseResponse<List<MediaUrlResponse>>

    /**
     * 获取banner列表
     */
    @POST("/game/banner/info")
    suspend fun getBannerInfo(): BaseResponse<List<BannerInfoResponse>>

    /**
     * 免打扰开关接口
     */
    @POST("/user/switchNotDisturb")
    suspend fun switchNotDisturb(@Body request: NotDisturbSwitchRequest): BaseResponse<Boolean>

    /**
     * 获取后置摄像头配置
     */
    @POST("/user/rearCamera/config")
    suspend fun getRearCameraConfig(): BaseResponse<RearCameraConfigResponse>

    /**
     * 开通后置摄像头
     */
    @POST("/user/rearCamera/open")
    suspend fun openRearCamera(@Body request: Any? = null): BaseResponse<Boolean>

    /**
     * 主播墙Banner接口
     */
    @POST("/game/banner/info")
    suspend fun getBroadcasterWallBanner(): BaseResponse<List<BroadcasterWallBannerResponse>>

    /**
     * 获取一个随机主播信息
     */
    @GET("/user/getRandomBroadcaster")
    suspend fun getRandomBroadcaster(): BaseResponse<UserInfo>

    /**
     * FlashChat 快速匹配接口
     */
    @POST("/video-call/flash/chat")
    suspend fun flashChat(@Body request: FlashChatRequest): BaseResponse<FlashChatResponse>

    /**
     * 订阅商品获取接口
     */
    @POST("/coin/subscription/search")
    suspend fun searchSubscriptions(@Body request: SubscriptionSearchRequest): BaseResponse<List<SubscriptionItemResponse>>

    /**
     * 账号删除
     */
    @POST("/user/deleteAccount")
    suspend fun deleteAccount(): BaseResponse<Boolean>

    @POST("/config/submitInstallReferer")
    suspend fun submitInstallReferrer(@Body args: InstallReferrerEntity): BaseResponse<Boolean>


    /**
     * 查询支付渠道
     */
    @POST("/coin/payChannel/getPostV2")
    suspend fun getChannelList(): BaseResponse<PayChannelResponse>

    /**
     * 获取用户访客分页列表接口
     */
    @POST("/user/v2/broadcasterRelations")
    suspend fun getUserFollowPage(@Body request: UserFollowPageRequest): BaseResponse<List<FollowModel>>

    /**
     * 日志上报接口
     */
    @POST("/log/liveChatPostV2")
    suspend fun reportLog(@Body request: LogReportRequest): BaseResponse<Boolean>

    /**
     * 客服默认问答列表
     * @return [BaseResponse<FAQInfoList>]
     */
    @POST("/user/FAQ/get")
    suspend fun getFaqList(): BaseResponse<FAQInfoList>

    /**
     * 获取关系数量统计接口
     */
    @POST("/user/relationsCounterPostV2")
    suspend fun getRelationsCounter(@Body requestBody: relationsCounterRequest): BaseResponse<RelationsCounter>

    /**
     * 注册奖励
     */
    @POST("/coin/presented/getPostV2")
    suspend fun getPresentedCoin(): BaseResponse<GetPresentedCoinResp>


    /**
     * 风控上报
     */
    @POST("/risk/info/upload")
    suspend fun uploadRiskInfo(@Body requestBody: UploadRiskInfoReqs): BaseResponse<Boolean>
}
