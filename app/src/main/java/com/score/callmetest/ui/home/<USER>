package com.score.callmetest.ui.home

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.score.callmetest.CallStatus
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SearchBroadcastersRequest
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.CountryUtils
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

class WallViewModel(
    private val tab1Name: String,
    private val tab2Name: String,
    private var regionCode: String? = null
): BaseViewModel() {
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val handler = Handler(Looper.getMainLooper())
    private val REFRESH_INTERVAL = 10_000L // 10秒
    private val CACHE_VALID_DURATION = 8_000L // 10秒
    private val SCROLL_STOP_DELAY = 500L // 0.5秒
    private val PAGE_SIZE = 20

    // 数据与状态
    private var currentPage = 1
    private var isLoading = false
    private var hasMore = true
    private val broadcasterList = CopyOnWriteArrayList<BroadcasterModel>()
    private val lastRefreshTimeMap = ConcurrentHashMap<String, Long>()
    private val lastStatusMap = ConcurrentHashMap<String, String>()

    // 回调接口
    var onDataChanged: ((List<BroadcasterModel>, Boolean) -> Unit)? = null
    var onStatusChanged: ((List<BroadcasterModel>) -> Unit)? = null
    var onError: ((String) -> Unit)? = null

    /**
     * 加载第一页（下拉刷新）
     */
    fun refresh() {
        currentPage = 1
        hasMore = true
        loadPage(isRefresh = true)
    }

    /**
     * 加载更多
     */
    fun loadMore() {
        if (!hasMore || isLoading) return
        currentPage++
        loadPage(isRefresh = false)
    }

    /**
     * 加载数据
     */
    private fun loadPage(isRefresh: Boolean) {
        if (isLoading) return
        isLoading = true
        coroutineScope.launch {
            try {

                val request = SearchBroadcastersRequest(
                    limit = PAGE_SIZE,
                    page = currentPage,
                    tag = tab2Name,
                    isPageMode = true,
                    isRemoteImageUrl = true,
                    category = tab1Name,
                    region = if (tab1Name == "Popular" && tab2Name == "All") regionCode else null
                )
                val response = RetrofitUtils.dataRepository.searchBroadcasters(request)
                if (response is NetworkResult.Success) {
                    val newList = response.data ?: emptyList()
                    hasMore = newList.size >= PAGE_SIZE
                    if (isRefresh) {
                        broadcasterList.clear()
                    }

                    // 适配审核模式
                    if (StrategyManager.isReviewPkg()) {
                        newList.forEach { reviewBcaster ->
                            if (reviewBcaster.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(reviewBcaster.userId)) {
                                reviewBcaster.status = CallStatus.ONLINE
                            }else {
                                reviewBcaster.status = GlobalManager.getReviewOtherStatus(reviewBcaster.userId)
                            }
                        }
                    }

                    broadcasterList.addAll(newList)
                    // 初始化状态缓存
                    newList.forEach { b ->
                        lastStatusMap.putIfAbsent(b.userId, b.status)
                        lastRefreshTimeMap.putIfAbsent(b.userId, System.currentTimeMillis())
                    }
                    onDataChanged?.invoke(broadcasterList.toList(), hasMore)
                } else {
                    onError?.invoke("加载主播失败")
                }
            } catch (e: Exception) {
                onError?.invoke(e.message ?: "加载主播异常")
            } finally {
                isLoading = false
            }
        }
    }
    /**
     * 移除被拉黑的用户并更新UI显示
     */
    fun removeBlockedUser(userId: String) {
        // 从列表中移除被拉黑的用户
        val removed = broadcasterList.removeAll { it.userId == userId }
        if (removed) {
            // 同时清理缓存
            lastStatusMap.remove(userId)
            lastRefreshTimeMap.remove(userId)
            // 更新UI
            onDataChanged?.invoke(broadcasterList.toList(), hasMore)
        }
    }

    /**
     * 刷新可见主播状态（带缓存）
     */
    fun refreshVisibleBroadcasters(visible: List<BroadcasterModel>) {
        val currentTime = System.currentTimeMillis()
        val toRefresh = visible.filter {
            val last = lastRefreshTimeMap[it.userId] ?: 0L
            currentTime - last >= CACHE_VALID_DURATION
        }
        if (toRefresh.isEmpty()) {
            // 直接用缓存
            onStatusChanged?.invoke(updateListWithCache(visible))
            return
        }
        coroutineScope.launch {
            try {
                val userIds = toRefresh.map { it.userId }
                val response = RetrofitUtils.dataRepository.getUserListOnlineStatusPostV2(
                    GetUserListOnlineStatusPostV2Request(userIds)
                )
                if (response is NetworkResult.Success) {
                    val newStatuses = response.data ?: emptyMap()
                    val now = System.currentTimeMillis()
                    newStatuses.forEach { (userId, status) ->
                        lastStatusMap[userId] = status
                        lastRefreshTimeMap[userId] = now
                    }
                    onStatusChanged?.invoke(updateListWithCache(visible))
                } else {
                    onStatusChanged?.invoke(updateListWithCache(visible))
                }
            } catch (e: Exception) {
                onStatusChanged?.invoke(updateListWithCache(visible))
            }
        }
    }

    private fun updateListWithCache(list: List<BroadcasterModel>): List<BroadcasterModel> {
        return list.map { b ->
            var status = lastStatusMap[b.userId] ?: b.status

            // 适配审核模式
            if (StrategyManager.isReviewPkg()) {
                getUserInfo(b.userId) { userInfo ->
                    if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                        status = CallStatus.ONLINE
                    }else {
                        status = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                    }
                }
            }

            if (b.status == status) b else b.copy(status = status)
        }
    }

    /**
     * 10秒定时刷新（Fragment可在onResume注册，onPause注销）
     */
    private var isTimerActive = false
    private var onPeriodicRefresh: (() -> Unit)? = null
    private val periodicRefreshRunnable = object : Runnable {
        override fun run() {
            if (isTimerActive) {
                onPeriodicRefresh?.invoke()
                handler.postDelayed(this, REFRESH_INTERVAL)
            }
        }
    }
    fun startStatusUpdates(refreshCallback: () -> Unit) {
        onPeriodicRefresh = refreshCallback
        isTimerActive = true
        handler.removeCallbacks(periodicRefreshRunnable)
        // 首次立即刷新
        refreshCallback()
        // 10秒后开始定时刷新
        handler.postDelayed(periodicRefreshRunnable, REFRESH_INTERVAL)
    }

    fun stopStatusUpdates() {
        isTimerActive = false
        handler.removeCallbacks(periodicRefreshRunnable)
        onPeriodicRefresh = null
    }

    /**
     * 滚动停止后500ms刷新
     */
    fun onScrollStateChanged(isScrolling: Boolean, refreshCallback: () -> Unit) {
        handler.removeCallbacksAndMessages(null)
        if (!isScrolling) {
            handler.postDelayed({ refreshCallback.invoke() }, SCROLL_STOP_DELAY)
        }
    }

    /**
     * 获取当前主播列表
     */
    fun getCurrentList(): List<BroadcasterModel> = broadcasterList.toList()
    fun hasMoreData(): Boolean = hasMore
    fun isLoading(): Boolean = isLoading

    /**
     * 更新国家筛选参数
     */
    fun updateRegion(region: String?) {
        if (regionCode != region) {
            regionCode = region
            // 重新加载数据
            refresh()
        }
    }
    
    /**
     * 清理资源
     */
    fun clear() {
        stopStatusUpdates()
        coroutineScope.cancel()
        broadcasterList.clear()
        lastRefreshTimeMap.clear()
        lastStatusMap.clear()
    }
} 