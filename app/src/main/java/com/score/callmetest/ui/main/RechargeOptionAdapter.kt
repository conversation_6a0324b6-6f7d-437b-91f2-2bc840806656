package com.score.callmetest.ui.main

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemRechargeOptionBinding
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Job
import java.lang.ref.WeakReference

class RechargeOptionAdapter(
    private var options: List<RechargeOption>,
    private val onClick: (RechargeOption) -> Unit,
    private val limitToSix: Boolean = false, // 参数控制是否限制6个
    private val onCountdownEnd: (() -> Unit)? = null // 用于通知计时结束
) : RecyclerView.Adapter<RechargeOptionAdapter.ViewHolder>() {

    // 处理后的展示数据（排序+可选限制6个）
    private val displayOptions: List<RechargeOption> by lazy {
        val sortedOptions = options.sortedByDescending { option ->
            when (option.type) {
                "2" -> 2 // 活动商品最高优先级
                "1" -> 1 // 促销商品中等优先级
                else -> 0 // 普通商品最低优先级
            }
        }

        if (limitToSix) {
            sortedOptions.take(6) // 限制6个
        } else {
            sortedOptions // 不限制数量
        }
    }
    // 提供两种创建方式
    companion object {
        // 方式1：排序并限制6个
        fun createWithLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)
        ): RechargeOptionAdapter {
            return RechargeOptionAdapter(options, onClick, true,onCountdownEnd)
        }

        // 方式2：只排序不限制数量
        fun createWithoutLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)
        ): RechargeOptionAdapter {
            return RechargeOptionAdapter(options, onClick, false,onCountdownEnd)
        }
    }


    private val countdownJobs = mutableMapOf<Int, Job>()

    // 活跃的ViewHolder弱引用集合
    private val activeViewHolders = mutableSetOf<WeakReference<ViewHolder>>()

    // 旋转动画相关常量
    private val ROTATE_DURATION = 6000L

    var selectedIndex: Int = 0
        set(value) {
            val old = field
            if (old != value) {
                field = value
                notifyItemChanged(old)
                notifyItemChanged(field)
            }
        }

    inner class ViewHolder(val binding: ItemRechargeOptionBinding) : RecyclerView.ViewHolder(binding.root) {
        var countdownJob: Job? = null
        // 添加旋转动画引用
        var rotateAnimator: ObjectAnimator? = null
        var scaleAnimatorSet: AnimatorSet? = null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemRechargeOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val option = options[position]
        holder.binding.ivIcon.setImageResource(option.iconRes)
        holder.binding.tvCoin.text = option.coinAmount.toString()

        // 设置价格信息
        setupPriceInfo(holder, option)

        if (option.extraCoinPercent != null && option.extraCoinPercent > 0) {
            holder.binding.tvBonus.text = "+${option.extraCoinPercent}%"
            holder.binding.imageCoin.visibility = View.VISIBLE
            holder.binding.tvBonus.visibility = View.VISIBLE
        } else {
            holder.binding.tvBonus.visibility = View.GONE
            holder.binding.imageCoin.visibility = View.GONE
        }

        // 设置HOT标签的可见性和内容
        setupHotLabel(holder, option, position)

        val selected = position == selectedIndex
        holder.itemView.isSelected = selected

        // 使用AnimatorUtil制作选中时的动效
        if (selected) {
            // 先清理之前的动画
            cancelIconAnimations(holder)

            // 显示背景动画容器
            holder.binding.bgAnimationImage.visibility = View.VISIBLE

            // 对图标添加缩放动画效果
            holder.scaleAnimatorSet = AnimatorUtil.scale(
                view = holder.binding.ivIcon,
                scaleFrom = 1f,
                scaleTo = 1.1f,
                scaleDuration = 300
            ) {
                // 缩放完成后开始背景容器的旋转动画
                holder.rotateAnimator = AnimatorUtil.rotate(
                    view = holder.binding.bgAnimationImage,
                    duration = ROTATE_DURATION,
                    repeatCount = ObjectAnimator.INFINITE
                )
            }
        } else {
            // 清理动画并恢复原始状态
            cancelIconAnimations(holder)

            // 隐藏背景动画容器
            holder.binding.bgAnimationImage.visibility = View.GONE

            // 恢复原始大小
            holder.scaleAnimatorSet = AnimatorUtil.scale(
                view = holder.binding.ivIcon,
                scaleFrom = holder.binding.ivIcon.scaleX,
                scaleTo = 1f,
                scaleDuration = 200
            )
        }

        // 设置UI布局和背景
        setupUILayoutAndBackground(holder, option, selected)

        holder.binding.tvPrice.setTextColor(Color.WHITE )

        // 设置点击事件
        setupClickEvents(holder, option, position)

        // 记录活跃ViewHolder
        activeViewHolders.add(WeakReference(holder))
    }

    override fun getItemCount(): Int = displayOptions.size


    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        // 清理倒计时任务
        holder.countdownJob?.cancel()
        holder.countdownJob = null

        // 清理图标动画
        cancelIconAnimations(holder)
        // 恢复图标原始状态
        holder.binding.ivIcon.scaleX = 1f
        holder.binding.ivIcon.scaleY = 1f
        holder.binding.ivIcon.rotation = 0f
        // 恢复背景动画容器原始状态
        holder.binding.bgAnimationImage.rotation = 0f
        holder.binding.bgAnimationImage.visibility = View.GONE
        // 移除活跃ViewHolder引用
        activeViewHolders.removeAll { it.get() == holder || it.get() == null }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        // 取消所有倒计时任务，防止内存泄漏
        countdownJobs.values.forEach { it.cancel() }
        countdownJobs.clear()
        // 取消所有活跃ViewHolder上的动画，彻底释放引用
        activeViewHolders.forEach { ref ->
            ref.get()?.let { cancelIconAnimations(it) }
        }
        activeViewHolders.clear()
        // 由于 ViewHolder 由 RecyclerView 管理，动画引用已在 onViewRecycled 释放
    }

    private fun setupHotLabel(holder: ViewHolder, option: RechargeOption, position: Int) {
        // 清理之前的倒计时任务
        holder.countdownJob?.cancel()
        holder.countdownJob = null
        countdownJobs[position]?.cancel()
        countdownJobs.remove(position)

        when (option.type) {
            "1", "2" -> {
                // 促销商品和活动商品：使用CountdownManager管理倒计时
                val goodsCode = option.goodsCode
                if (!goodsCode.isNullOrEmpty() && option.remainMilliseconds != null && option.remainMilliseconds > 0) {
                    // 初始化或更新倒计时状态（使用商品促销类型）
                    CountdownManager.initOrUpdateCountdown(
                        CountdownManager.ActivityType.GOODS_PROMOTION,
                        goodsCode,
                        option.remainMilliseconds
                    )

                    // 检查是否已过期
                    if (CountdownManager.isExpired(CountdownManager.ActivityType.GOODS_PROMOTION, goodsCode)) {
                        holder.binding.tvHotRed.visibility = View.GONE
                        onCountdownEnd?.invoke()
                    } else {
                        holder.binding.tvHotRed.visibility = View.VISIBLE
                        startCountdown(holder, goodsCode, position)
                    }
                } else {
                    holder.binding.tvHotRed.visibility = View.GONE
                }
            }
            else -> {
                // 其他类型：显示原有的tags逻辑
                if (!option.tags.isNullOrEmpty()) {
                    holder.binding.tvHotRed.visibility = View.VISIBLE
                    holder.binding.tvHotRed.text = option.tags
                } else {
                    holder.binding.tvHotRed.visibility = View.GONE
                }
            }
        }
    }

    private fun startCountdown(holder: ViewHolder, goodsCode: String, position: Int) {
        val countdownJob = CountdownManager.startCountdown(
            activityType = CountdownManager.ActivityType.GOODS_PROMOTION,
            activityId = goodsCode,
            onTick = { remainingMillis ->
                ThreadUtils.runOnMain {
                    val timeText = TimeUtils.formatTime(remainingMillis)
                    val spannableText = createCoinWithTimeText(holder, timeText)
                    holder.binding.tvHotRed.text = spannableText
                }
            },
            onFinish = {
                ThreadUtils.runOnMain {
                    // 倒计时结束
                    holder.binding.tvHotRed.visibility = View.GONE
                    holder.countdownJob = null
                    countdownJobs.remove(position)

                    // 通知外部计时结束
                    onCountdownEnd?.invoke()
                }
            }
        )

        holder.countdownJob = countdownJob
        if (countdownJob != null) {
            countdownJobs[position] = countdownJob
        }
    }

    private fun createCoinWithTimeText(holder: ViewHolder, timeText: String): SpannableString {
        val text = " $timeText"
        val spannable = SpannableString(text)

        // 添加倒计时图片
        val drawable = ContextCompat.getDrawable(holder.itemView.context, R.drawable.clock)
        drawable?.setBounds(0, 0, DisplayUtils.dp2px(14f), DisplayUtils.dp2px(14f))
        val imageSpan = drawable?.let { ImageSpan(it, ImageSpan.ALIGN_BOTTOM) }

        if (imageSpan != null) {
            spannable.setSpan(
                imageSpan,
                0,
                1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannable
    }

    /**
     * 取消图标动画(金币闪光)
     */
    private fun cancelIconAnimations(holder: ViewHolder) {
        // 统一安全释放动画，彻底断开与 View 的引用，防止内存泄漏
        AnimatorUtil.releaseAnimator(holder.rotateAnimator)
        holder.rotateAnimator = null

        AnimatorUtil.releaseAnimator(holder.scaleAnimatorSet)
        holder.scaleAnimatorSet = null

        // 重置图标状态
        holder.binding.ivIcon.run {
            animate().cancel()
            clearAnimation()
        }

        // 重置背景动画容器状态
        holder.binding.bgAnimationImage.run {
            animate().cancel()
            clearAnimation()
            rotation = 0f
            visibility = View.GONE
        }
    }

    /**
     * 设置价格信息（根据商品类型优化判断逻辑）
     */
    private fun setupPriceInfo(holder: ViewHolder, option: RechargeOption) {
        // 设置当前价格
        holder.binding.tvPrice.text = option.price
        holder.binding.ordinaryTvPrice.text = option.price

        // 根据商品类型设置旧价格信息
        when (option.type) {
            "1", "2" -> {
                // 促销商品和活动商品：只设置 priceLayout 中的旧价格
                setupOldPrice(
                    priceTextView = holder.binding.tvOldPrice,
                    option = option
                )
            }
            else -> {
                // 普通商品：只设置 ordinaryPriceLayout 中的旧价格
                setupOldPrice(
                    priceTextView = holder.binding.ordinaryTvOldPrice,
                    option = option
                )
            }
        }
    }

    /**
     * 设置旧价格的显示和下划线
     */
    private fun setupOldPrice(priceTextView: TextView, option: RechargeOption) {
        if (option.oldPrice != null && option.oldPrice != option.price) {
            priceTextView.text = option.oldPrice
            priceTextView.paint.isStrikeThruText = true
            priceTextView.visibility = View.VISIBLE
        } else {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
        }
    }

    /**
     * 根据商品类型设置UI布局和背景
     */
    private fun setupUILayoutAndBackground(holder: ViewHolder, option: RechargeOption, selected: Boolean) {
        when (option.type) {
            "1", "2" -> setupPromotionProductUI(holder)
            else -> setupNormalProductUI(holder, selected)
        }
    }

    /**
     * 设置促销商品和活动商品的UI
     */
    private fun setupPromotionProductUI(holder: ViewHolder) {
        // 显示促销价格布局，隐藏普通价格布局
        holder.binding.priceLayout.visibility = View.VISIBLE
        holder.binding.ordinaryPriceLayout.visibility = View.INVISIBLE

        // 设置价格布局背景（始终为黑色）
        GlobalManager.setViewRoundBackground(
            holder.binding.priceLayout,
            Color.BLACK
        )

        // 设置卡片紫色背景
        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = "#C19BFF".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置普通商品的UI
     */
    private fun setupNormalProductUI(holder: ViewHolder, selected: Boolean) {
        // 隐藏促销价格布局，显示普通价格布局
        holder.binding.priceLayout.visibility = View.INVISIBLE
        holder.binding.ordinaryPriceLayout.visibility = View.VISIBLE

        // 设置普通布局背景
        holder.binding.ordinaryPriceLayout.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#FFF4CD".toColorInt(), "#FFFEDB".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )

        // 设置卡片白色背景（普通商品始终保持白色）
        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = Color.WHITE,
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置点击事件
     */
    private fun setupClickEvents(holder: ViewHolder, option: RechargeOption, position: Int) {
        // 设置整个item的点击事件（选中逻辑 + 支付逻辑）
        holder.itemView.click {
            if (selectedIndex != position) {
                selectedIndex = position
            }
            // 直接跳转支付界面
            onClick(option)
        }
    }
}