package com.score.callmetest.ui.web

import android.os.Build
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import com.score.callmetest.databinding.ActivityWebViewBinding
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import timber.log.Timber

class WebViewActivity : BaseActivity<ActivityWebViewBinding, EmptyViewModel>() {

    override fun getViewBinding(): ActivityWebViewBinding {
        return ActivityWebViewBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        onBackPressedDispatcher.addCallback(this){
            if (binding.webView.canGoBack()) {
                binding.webView.goBack()
            } else {
                finish()
            }
        }

        setupWebView()
        setupToolbar()
    }

    private fun setupWebView() {
        val url = intent.getStringExtra("url") ?: "https://example.com"
        val title = intent.getStringExtra("title") ?: "Web Page"

        binding.toolbar.title = title

        binding.webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
        }

        binding.webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                url?.let { view?.loadUrl(it) }
                return true
            }
        }

        binding.webView.loadUrl(url)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        binding.toolbar.setNavigationOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }
    }

}